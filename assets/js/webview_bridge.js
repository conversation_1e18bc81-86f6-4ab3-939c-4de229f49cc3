/**
 * WebView JavaScript Bridge
 * 提供Flutter与WebView之间的通信桥梁
 */

window.JSExecutionHelper = {
  /**
   * 报告JavaScript执行结果
   */
  reportResult: function(success, message, executionId) {
    if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
      FlutterBridge.postMessage(JSON.stringify({
        type: 'jsExecutionResult',
        success: success,
        message: message,
        executionId: executionId || 'unknown'
      }));
    } else {
      console.error('FlutterBridge channel not available');
    }
  },
  
  /**
   * 报告内容变化
   */
  reportContentChange: function(data) {
    if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
      FlutterBridge.postMessage(JSON.stringify({
        type: 'contentEditable_changed',
        data: data
      }));
    } else {
      console.error('FlutterBridge channel not available for content change');
    }
  },
  
  /**
   * 设置contentEditable监控
   */
  setupContentMonitor: function() {
    console.log('开始设置contentEditable监控');
    
    // 防抖函数，避免频繁触发
    let debounceTimer = null;
    const debounceDelay = 1000; // 1秒防抖
    
    function debounceContentChange(element) {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      
      debounceTimer = setTimeout(function() {
        window.JSExecutionHelper.reportContentChange({
          type: 'manual_edit',
          content: element.textContent || '',
          html: element.innerHTML || '',
          tagName: element.tagName || '',
          timestamp: Date.now()
        });
      }, debounceDelay);
    }
    
    // 监听input事件（主要的内容变化事件）
    document.addEventListener('input', function(event) {
      if (event.target.contentEditable === 'true') {
        console.log('检测到contentEditable内容变化:', event.target.tagName);
        debounceContentChange(event.target);
      }
    });
    
    // 监听paste事件（粘贴内容）
    document.addEventListener('paste', function(event) {
      if (event.target.contentEditable === 'true') {
        console.log('检测到contentEditable粘贴操作:', event.target.tagName);
        // 粘贴后稍微延迟一下再获取内容，确保粘贴完成
        setTimeout(function() {
          debounceContentChange(event.target);
        }, 100);
      }
    });
    
    // 监听blur事件（失去焦点时保存）
    document.addEventListener('blur', function(event) {
      if (event.target.contentEditable === 'true') {
        console.log('检测到contentEditable失去焦点:', event.target.tagName);
        // 清除防抖定时器，立即保存
        if (debounceTimer) {
          clearTimeout(debounceTimer);
          debounceTimer = null;
        }
        window.JSExecutionHelper.reportContentChange({
          type: 'manual_edit_blur',
          content: event.target.textContent || '',
          html: event.target.innerHTML || '',
          tagName: event.target.tagName || '',
          timestamp: Date.now()
        });
      }
    });
    
    console.log('contentEditable监控设置完成');
  }
};

console.log('JSExecutionHelper注入成功');

// 自动设置内容监控
if (window.JSExecutionHelper && window.JSExecutionHelper.setupContentMonitor) {
  window.JSExecutionHelper.setupContentMonitor();
}
