import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import 'app/index.dart';
import 'notes_page.dart';
// import 'ai_chat_page/index.dart'; // 暂时注释掉AI页面

/// 主要的底部导航页面
class MainTabPage extends StatefulWidget {
  const MainTabPage({super.key});

  @override
  State<MainTabPage> createState() => _MainTabPageState();
}

class _MainTabPageState extends State<MainTabPage> {
  int _currentIndex = 0;
  late PageController _pageController;
  bool _showBottomNav = true; // 控制底部导航栏显示

  // 页面列表
  final List<Widget> _pages = [
    const HomePage(title: '首页'),
    const NotesPage(),
    // const AiChatPage(), // 暂时注释掉AI页面
  ];



  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _currentIndex);
    _setStatusBarColor();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 设置状态栏颜色
  void _setStatusBarColor() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white, // 设置底部系统导航栏为白色
        systemNavigationBarIconBrightness: Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent, // 设置分割线为透明
        systemNavigationBarContrastEnforced: false, // 禁用系统强制对比度
      ),
    );
  }

  /// 切换页面
  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
        // 现在只有两个tab，都显示底部导航栏
        _showBottomNav = true;
      });
      // 使用jumpToPage而不是animateToPage，避免动画效果
      _pageController.jumpToPage(index);
    }
  }

  /// 页面滑动回调
  void _onPageChanged(int index) {
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
        // 现在只有两个tab，都显示底部导航栏
        _showBottomNav = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 现在只有两个tab，直接允许默认返回行为
        return true; // 允许默认返回行为
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: PageView(
          controller: _pageController,
          onPageChanged: _onPageChanged,
          physics: const NeverScrollableScrollPhysics(), // 禁用滑动手势
          children: _pages,
        ),
        bottomNavigationBar: _showBottomNav ? _buildCustomBottomNavigationBar() : null,
      ),
    );
  }

  /// 构建自定义底部导航栏
  Widget _buildCustomBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 16,
            offset: const Offset(0, -4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.only(
          left: 16.w,
          right: 16.w,
          top: 4.h,
          bottom: 4.h + MediaQuery.of(context).padding.bottom,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildTabItem(
              index: 0,
              icon: Icons.home_outlined,
              activeIcon: Icons.home,
              label: '首页',
            ),
            _buildTabItem(
              index: 1,
              icon: Icons.edit_note_outlined,
              activeIcon: Icons.edit_note,
              label: '笔记',
            ),
            // 暂时注释掉AI tab
            // _buildTabItem(
            //   index: 2,
            //   icon: Icons.psychology_outlined,
            //   activeIcon: Icons.psychology,
            //   label: 'AI',
            // ),
          ],
        ),
      ),
    );
  }

  /// 构建单个Tab项
  Widget _buildTabItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
  }) {
    final isSelected = _currentIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => _onTabTapped(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 2.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 图标 - 适当放大，保持简洁设计
              Icon(
                isSelected ? activeIcon : icon,
                size: 30.r, // 从22.r增加到26.r
                color: isSelected
                  ? AppColors.primary
                  : AppColors.textSecondary.withOpacity(0.6),
              ),
              // SizedBox(height: 2.h), // 减少间距使tab更矮
              // 标签文字 - 简洁设计，只改变颜色
              Text(
                label,
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected
                    ? AppColors.primary
                    : AppColors.textSecondary.withOpacity(0.6),
                  height: 1.0,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
