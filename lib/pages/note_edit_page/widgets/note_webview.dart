import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../models/note_edit_model.dart';

/// 笔记WebView组件
class NoteWebview extends StatefulWidget {
  /// 笔记数据模型
  final NoteEditModel noteModel;
  
  /// 页面开始加载回调
  final VoidCallback? onPageStarted;
  
  /// 页面加载完成回调
  final VoidCallback? onPageFinished;
  
  /// 页面加载错误回调
  final Function(String error)? onPageError;

  /// JavaScript执行回调
  final Function(String script)? onExecuteScript;

  const NoteWebview({
    super.key,
    required this.noteModel,
    this.onPageStarted,
    this.onPageFinished,
    this.onPageError,
    this.onExecuteScript,
  });

  @override
  State<NoteWebview> createState() => NoteWebviewState();
}

class NoteWebviewState extends State<NoteWebview> {
  late final WebViewController _webViewController;

  // JavaScript执行结果的Completer
  Completer<bool>? _jsExecutionCompleter;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  /// 执行JavaScript脚本
  Future<bool> executeScript(String script) async {
    try {
      print('准备执行JavaScript脚本: $script');

      // 创建新的Completer等待JavaScript执行结果
      _jsExecutionCompleter = Completer<bool>();

      // 生成唯一的执行ID，用于区分不同的执行请求
      final executionId = DateTime.now().millisecondsSinceEpoch.toString();

      // 简化的JavaScript包装脚本
      final wrappedScript = '''
try {
  $script;
  if (window.JSExecutionHelper) {
    window.JSExecutionHelper.reportResult(true, 'SUCCESS', '$executionId');
  }
} catch (error) {
  console.error('JavaScript执行错误:', error);
  if (window.JSExecutionHelper) {
    window.JSExecutionHelper.reportResult(false, error.name + ': ' + error.message, '$executionId');
  }
}
      ''';

      // 执行JavaScript
      await _webViewController.runJavaScript(wrappedScript);

      // 等待JavaScript执行结果回调，设置超时时间
      final result = await _jsExecutionCompleter!.future.timeout(
        Duration(seconds: 5),
        onTimeout: () {
          print('JavaScript执行超时，执行ID: $executionId');
          return false;
        },
      );

      print('JavaScript执行完成，执行ID: $executionId, 结果: $result');
      return result;
    } catch (e) {
      print('执行JavaScript失败: $e');
      return false;
    } finally {
      _jsExecutionCompleter = null;
    }
  }

  /// 获取当前页面的最新DOM结构
  Future<String?> getLatestDom() async {
    try {
      print('准备获取最新DOM结构');

      // 只获取body内容，避免模板占位符和头部信息问题
      final result = await _webViewController.runJavaScriptReturningResult('''(function() {return document.documentElement.outerHTML;})();''');


      if (result != null) {
        // 在这里就进行解码，避免存储转义后的内容
        final decodedDom = _decodeHtmlString(domString);

        return decodedDom;
      } else {
        print('获取DOM结构失败: 返回结果为空');
        return null;
      }
    } catch (e) {
      print('获取DOM结构失败: $e');
      return null;
    }
  }

  /// 解码HTML字符串，处理WebView返回的转义字符
  String _decodeHtmlString(String htmlString) {
    // 去除转义后的html内容中，第一个和最后一个双引号，"xxxx" -> xxxx
    String processedString = htmlString;

    // 检查字符串是否以双引号开头和结尾，如果是则去除
    if (processedString.length >= 2 &&
        processedString.startsWith('"') &&
        processedString.endsWith('"')) {
      processedString = processedString.substring(1, processedString.length - 1);
    }

    return processedString
        // 处理Unicode转义
        .replaceAll('\\u003C', '<')
        .replaceAll('\\u003E', '>')
        .replaceAll('\\u0026', '&')
        .replaceAll('\\u0022', '"')
        .replaceAll('\\u0027', "'")
        .replaceAll('\\u002F', '/')
        .replaceAll('\\u003D', '=')
        // 处理常规转义
        .replaceAll('\n', '')
        .replaceAll('\\n', '\n')
        .replaceAll('\\r', '\r')
        .replaceAll('\\t', '\t')
        .replaceAll('\\"', '"')
        .replaceAll("\\'", "'")
        .replaceAll('\\\\', '\\');
  }

  /// 加载HTML内容到WebView
  Future<void> loadHtmlContent(String htmlContent) async {
    try {
      // 方案1：直接使用loadHtmlString完整替换（推荐）
      await _webViewController.loadHtmlString(htmlContent);

      // 方案2：如果需要手动清空，可以用以下方式（备选）
      // await _webViewController.runJavaScript('''
      //   document.open();
      //   document.write('');
      //   document.close();
      // ''');
      // await Future.delayed(Duration(milliseconds: 100));
      // await _webViewController.loadHtmlString(htmlContent);
      print('HTML内容加载完成');
    } catch (e) {
      print('加载HTML内容失败: $e');
    }
  }

  @override
  void didUpdateWidget(NoteWebview oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 如果笔记内容发生变化，重新加载
    if (oldWidget.noteModel.htmlContent != widget.noteModel.htmlContent) {
      _loadContent();
    }
  }

  /// 初始化WebView
  void _initializeWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            widget.onPageStarted?.call();
          },
          onPageFinished: (String url) {
            // 页面加载完成后注入JavaScript Bridge
            _injectJavaScriptBridge();
            widget.onPageFinished?.call();
          },
          onWebResourceError: (WebResourceError error) {
            widget.onPageError?.call('加载失败: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterBridge',
        onMessageReceived: (JavaScriptMessage message) {
          _handleJavaScriptMessage(message);
        },
      );

    // 加载内容
    _loadContent();
  }

  /// 注入JavaScript Bridge
  void _injectJavaScriptBridge() {
final bridgeScript = '''
  window.JSExecutionHelper = {
    reportResult: function(success, message, executionId) {
      if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
        FlutterBridge.postMessage(JSON.stringify({
          type: 'jsExecutionResult',
          success: success,
          message: message,
          executionId: executionId || 'unknown'
        }));
      } else {
        console.error('FlutterBridge channel not available');
      }
    },

    reportContentChange: function(data) {
      if (typeof FlutterBridge === 'object' && FlutterBridge.postMessage) {
        FlutterBridge.postMessage(JSON.stringify({
          type: 'contentEditable_changed',
          data: data
        }));
      } else {
        console.error('FlutterBridge channel not available for content change');
      }
    },

    setupContentMonitor: function() {
      console.log('开始设置contentEditable监控');

      // 防抖函数，避免频繁触发
      let debounceTimer = null;
      const debounceDelay = 1000; // 1秒防抖

      function debounceContentChange(element) {
        if (debounceTimer) {
          clearTimeout(debounceTimer);
        }

        debounceTimer = setTimeout(function() {
          window.JSExecutionHelper.reportContentChange({
            type: 'manual_edit',
            content: element.textContent || '',
            html: element.innerHTML || '',
            tagName: element.tagName || '',
            timestamp: Date.now()
          });
        }, debounceDelay);
      }

      // 监听input事件（主要的内容变化事件）
      document.addEventListener('input', function(event) {
        if (event.target.contentEditable === 'true') {
          console.log('检测到contentEditable内容变化:', event.target.tagName);
          debounceContentChange(event.target);
        }
      });

      // 监听paste事件（粘贴内容）
      document.addEventListener('paste', function(event) {
        if (event.target.contentEditable === 'true') {
          console.log('检测到contentEditable粘贴操作:', event.target.tagName);
          // 粘贴后稍微延迟一下再获取内容，确保粘贴完成
          setTimeout(function() {
            debounceContentChange(event.target);
          }, 100);
        }
      });

      // 监听blur事件（失去焦点时保存）
      document.addEventListener('blur', function(event) {
        if (event.target.contentEditable === 'true') {
          console.log('检测到contentEditable失去焦点:', event.target.tagName);
          // 清除防抖定时器，立即保存
          if (debounceTimer) {
            clearTimeout(debounceTimer);
            debounceTimer = null;
          }
          window.JSExecutionHelper.reportContentChange({
            type: 'manual_edit_blur',
            content: event.target.textContent || '',
            html: event.target.innerHTML || '',
            tagName: event.target.tagName || '',
            timestamp: Date.now()
          });
        }
      });

      console.log('contentEditable监控设置完成');
    }
  };

  console.log('JSExecutionHelper注入成功');

  // 自动设置内容监控
  if (window.JSExecutionHelper && window.JSExecutionHelper.setupContentMonitor) {
    window.JSExecutionHelper.setupContentMonitor();
  }
''';

    _webViewController.runJavaScript(bridgeScript);
  }

  /// 处理JavaScript消息
  void _handleJavaScriptMessage(JavaScriptMessage message) {
    try {
      final data = jsonDecode(message.message) as Map<String, dynamic>;
      final type = data['type'] as String?;

      if (type == 'jsExecutionResult') {
        final success = data['success'] as bool? ?? false;
        final resultMessage = data['message'] as String? ?? '';
        final executionId = data['executionId'] as String? ?? 'unknown';

        print('收到JavaScript执行结果: success=$success, message=$resultMessage, executionId=$executionId');

        // 完成Completer
        if (_jsExecutionCompleter != null && !_jsExecutionCompleter!.isCompleted) {
          _jsExecutionCompleter!.complete(success);
        }
      } else if (type == 'contentEditable_changed') {
        final changeData = data['data'] as Map<String, dynamic>?;
        if (changeData != null) {
          _handleContentEditableChange(changeData);
        }
      }
    } catch (e) {
      print('处理JavaScript消息失败: $e');
      // 如果解析失败，认为执行失败
      if (_jsExecutionCompleter != null && !_jsExecutionCompleter!.isCompleted) {
        _jsExecutionCompleter!.complete(false);
      }
    }
  }

  /// 处理contentEditable内容变化
  void _handleContentEditableChange(Map<String, dynamic> data) {
    final changeType = data['type'] as String? ?? '';
    final content = data['content'] as String? ?? '';
    final html = data['html'] as String? ?? '';
    final tagName = data['tagName'] as String? ?? '';
    final timestamp = data['timestamp'] as int? ?? 0;

    print('ContentEditable内容变化: type=$changeType, tagName=$tagName, content长度=${content.length}');

    // 触发自动保存草稿
    _autoSaveDraft();
  }

  /// 自动保存草稿
  Future<void> _autoSaveDraft() async {
    try {
      // 获取当前页面的完整HTML内容
      final htmlContent = await getLatestDom();
      if (htmlContent == null || htmlContent.isEmpty) {
        print('获取页面内容失败，无法自动保存草稿');
        return;
      }

      // 获取笔记ID
      final noteId = widget.noteModel.noteId;
      if (noteId.isEmpty) {
        print('笔记ID为空，无法自动保存草稿');
        return;
      }

      print('开始自动保存草稿: noteId=$noteId, 内容长度=${htmlContent.length}');

      // 调用草稿API保存
      // 这里需要引入ApiProvider，但为了避免循环依赖，我们通过回调的方式处理
      widget.onContentChanged?.call(htmlContent);

    } catch (e) {
      print('自动保存草稿失败: $e');
    }
  }

  /// 加载内容
  void _loadContent() {
    if (widget.noteModel.hasHtmlContent) {
      // 如果有HTML内容，直接加载
      _webViewController.loadHtmlString(widget.noteModel.htmlContent);
    } else {
      // 如果没有HTML内容，生成备用HTML
      final fallbackHtml = _buildFallbackHtml();
      _webViewController.loadHtmlString(fallbackHtml);
    }
  }

  /// 构建备用HTML内容
  String _buildFallbackHtml() {
    final model = widget.noteModel;
    
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${model.title}</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #fff;
                padding: 20px;
            }
            
            .container {
                max-width: 800px;
                margin: 0 auto;
            }
            
            .title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                color: #1a1a1a;
            }
            
            .cover {
                width: 100%;
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            .desc {
                font-size: 16px;
                color: #666;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #1EB9EF;
            }
            
            .content {
                font-size: 16px;
                line-height: 1.8;
                white-space: pre-wrap;
            }
            
            .empty-content {
                text-align: center;
                color: #999;
                font-style: italic;
                padding: 40px 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="title">${model.title.isNotEmpty ? model.title : '无标题'}</div>
            ${model.hasCover ? '<img class="cover" src="${model.cover}" alt="封面图片" />' : ''}
            ${model.hasDescription ? '<div class="desc">${model.description}</div>' : ''}
            ${model.content.isNotEmpty ? '<div class="content">${model.content}</div>' : '<div class="empty-content">暂无内容</div>'}
        </div>
    </body>
    </html>
    ''';
  }

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(controller: _webViewController);
  }
}
