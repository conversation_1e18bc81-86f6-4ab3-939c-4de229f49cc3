import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../constants/app_colors.dart';
import '../../utils/note_webview_helper.dart';
import '../../widgets/custom_toast.dart';
import '../../components/publish_confirmation_dialog.dart';
import '../../api/api_provider.dart';
import 'models/note_edit_model.dart';
import 'widgets/note_webview.dart';
import 'widgets/note_loading.dart';
import 'widgets/note_error.dart';

import 'widgets/note_action_buttons.dart';
import 'widgets/note_bottom_panel.dart';

/// 编辑笔记页面
class NoteEditPage extends StatefulWidget {
  /// 笔记ID
  final String? noteId;

  const NoteEditPage({
    super.key,
    this.noteId,
  });

  @override
  State<NoteEditPage> createState() => _NoteEditPageState();
}

class _NoteEditPageState extends State<NoteEditPage> {
  /// 笔记数据模型
  NoteEditModel _noteModel = const NoteEditModel(noteId: '');

  /// 笔记WebView辅助类
  final NoteWebviewHelper _noteHelper = NoteWebviewHelper();

  /// 底部面板是否显示
  bool _isPanelVisible = false;

  /// 是否为预览模式
  bool _isPreviewMode = false;

  /// WebView组件的GlobalKey，用于调用其方法
  final GlobalKey<NoteWebviewState> _webviewKey = GlobalKey<NoteWebviewState>();

  bool _hasLoadedContent = false;

  /// 是否是从草稿进入的编辑页面
  bool _isDraftMode = false;

  /// 是否启用了手动编辑模式
  bool _isManualEditEnabled = false;

  /// 历史记录数组，用于撤销功能
  final List<String> _historyStack = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasLoadedContent) {
      _hasLoadedContent = true;
      _loadNoteContent();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }



  /// 加载笔记内容
  Future<void> _loadNoteContent() async {
    // 获取路由参数
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final isDraft = args?['isDraft'] as bool? ?? false;
    final draftContent = args?['draftContent'] as String?;
    final draftTitle = args?['draftTitle'] as String?;
    final draftDesc = args?['draftDesc'] as String?;
    final draftCover = args?['draftCover'] as String?;

    // 获取笔记ID - 优先从路由参数获取，其次从widget参数获取
    final noteId = args?['noteId'] as String? ?? widget.noteId ?? '2';

    debugPrint('编辑页面参数解析: noteId=$noteId, isDraft=$isDraft, draftContent长度=${draftContent?.length ?? 0}');
    debugPrint('草稿标题: $draftTitle, 草稿描述: $draftDesc');
    debugPrint('路由参数: $args');

    setState(() {
      _noteModel = _noteModel.copyWith(
        noteId: noteId,
        state: NoteEditState.loading,
        errorMessage: null,
      );
      // 记录是否是草稿模式
      _isDraftMode = isDraft;
    });

    try {
      if (isDraft && draftContent != null && draftContent.isNotEmpty) {
        // 如果是草稿模式且有草稿内容，直接使用草稿内容
        debugPrint('使用草稿内容，长度: ${draftContent.length}');
        setState(() {
          _noteModel = _noteModel.copyWith(
            title: draftTitle ?? '无标题',
            htmlContent: draftContent,
            description: draftDesc ?? '',
            cover: draftCover ?? '',
            state: NoteEditState.success,
          );
        });
      } else {
        // 否则按原来的逻辑获取笔记内容
        // 首先尝试获取HTML内容
        final html = await _noteHelper.getNoteHtml(noteId);

        if (html != null && html.isNotEmpty) {
          // 如果有HTML内容，直接使用
          setState(() {
            _noteModel = _noteModel.copyWith(
              htmlContent: html,
              state: NoteEditState.success,
            );
          });
        } else {
          // 如果没有HTML内容，获取完整的笔记详情
          final noteDetail = await _noteHelper.getNoteDetail(noteId);

          if (noteDetail != null) {
            setState(() {
              _noteModel = NoteEditModel.fromJson(noteDetail);
            });
          } else {
            setState(() {
              _noteModel = _noteModel.copyWith(
                state: NoteEditState.error,
                errorMessage: '无法获取笔记内容',
              );
            });
          }
        }
      }
    } catch (e) {
      setState(() {
        _noteModel = _noteModel.copyWith(
          state: NoteEditState.error,
          errorMessage: '加载笔记内容失败: $e',
        );
      });
    }
  }

  /// WebView开始加载回调
  void _onWebViewPageStarted() {
    // 可以在这里添加额外的加载逻辑
  }

  /// WebView加载完成回调
  void _onWebViewPageFinished() {
    // 如果是草稿模式且还未启用手动编辑，则启用它
    if (_isDraftMode && !_isManualEditEnabled) {
      Future.delayed(Duration(milliseconds: 300), () {
        if (mounted) {
          _enableManualEdit();
        }
      });
    }
  }

  /// WebView加载错误回调
  void _onWebViewPageError(String error) {
    setState(() {
      _noteModel = _noteModel.copyWith(
        state: NoteEditState.error,
        errorMessage: error,
      );
    });
  }

  /// 重试加载
  void _onRetry() {
    _loadNoteContent();
  }

  /// 返回上一页
  void _onBack() {
    if (_isPreviewMode) {
      // 如果在预览模式，先退出预览模式
      setState(() {
        _isPreviewMode = false;
      });
      CustomToast.show('已退出预览模式');
    } else {
      Navigator.of(context).pop();
    }
  }

  /// 打开底部面板（AI编辑）
  void _onOpenPanel() {
    setState(() {
      _isPanelVisible = true;
    });
  }



  /// 关闭底部面板
  void _onClosePanel() {
    setState(() {
      _isPanelVisible = false;
    });
  }

  /// 执行JavaScript脚本
  Future<bool> _onExecuteScript(String script) async {
    try {
      // 在执行脚本之前，先获取当前HTML并保存到历史记录
      final currentHtml = await _webviewKey.currentState?.getLatestDom();
      if (currentHtml != null && currentHtml.isNotEmpty) {
        _historyStack.add(currentHtml);
        print('已保存当前HTML到历史记录，历史记录数量: ${_historyStack.length}');

        // 限制历史记录数量，避免内存过大（保留最近10次）
        if (_historyStack.length > 10) {
          _historyStack.removeAt(0);
        }
      }

      // 执行JavaScript脚本
      final success = await _webviewKey.currentState?.executeScript(script);
      if (success == true) {
        print('JavaScript脚本执行成功');

        // AI编辑成功后自动保存草稿
        _autoSaveDraftAfterAiEdit();

        // 触发UI更新，显示撤销按钮
        setState(() {});
        return true;
      } else {
        print('JavaScript脚本执行失败');
        // 如果执行失败，移除刚才添加的历史记录
        if (_historyStack.isNotEmpty) {
          _historyStack.removeLast();
        }
        return false;
      }
    } catch (e) {
      print('执行JavaScript脚本异常: $e');
      // 如果出现异常，移除刚才添加的历史记录
      if (_historyStack.isNotEmpty) {
        _historyStack.removeLast();
      }
      return false;
    }
  }

  /// 获取最新DOM结构
  Future<String?> _onGetLatestDom() async {
    final domContent = await _webviewKey.currentState?.getLatestDom();
    if (domContent != null) {
      print('成功获取DOM结构，完整内容: $domContent');
      return domContent;
    } else {
      print('获取DOM结构失败');
      return null;
    }
  }

  /// 处理回滚操作
  void _onRollback() {
    // 暂时置空
  }

  /// 处理对比操作
  void _onCompare() {
    // 暂时置空
  }

  /// 处理撤销操作
  Future<void> _onUndo() async {
    if (_historyStack.isEmpty) {
      print('没有可撤销的历史记录');
      return;
    }

    try {
      // 获取最后一个历史记录
      final lastHtml = _historyStack.removeLast();
      print('开始撤销操作，恢复到历史记录，剩余历史记录数量: ${_historyStack.length}');
      print('撤销HTML内容长度: ${lastHtml.length}');
      print('撤销HTML内容前100字符: ${lastHtml.length > 100 ? lastHtml.substring(0, 100) : lastHtml}');

      // 将HTML内容加载到WebView
      await _webviewKey.currentState?.loadHtmlContent(lastHtml);

      // 更新状态，隐藏撤销按钮（如果没有更多历史记录）
      setState(() {});

      print('撤销操作完成');
    } catch (e) {
      print('撤销操作失败: $e');
    }
  }

  /// 处理预览操作
  void _onPreview() {
    setState(() {
      _isPreviewMode = !_isPreviewMode;
    });

    if (_isPreviewMode) {
      // 进入预览模式时，如果启用了手动编辑，先禁用它
      if (_isManualEditEnabled) {
        _disableManualEdit();
      }
      // 隐藏AI编辑面板
      if (_isPanelVisible) {
        _onClosePanel();
      }
      CustomToast.show('已进入预览模式，返回可退出预览');
    } else {
      // 退出预览模式时，如果是草稿模式，重新启用手动编辑
      if (_isDraftMode) {
        Future.delayed(Duration(milliseconds: 100), () {
          if (mounted) {
            _enableManualEdit();
          }
        });
      }
      CustomToast.show('已退出预览模式');
    }
  }

  /// 启用手动编辑模式
  Future<void> _enableManualEdit() async {
    const script = '''
      // 添加CSS样式隐藏contentEditable的聚焦边框
      if (!document.getElementById('contenteditable-style')) {
        const style = document.createElement('style');
        style.id = 'contenteditable-style';
        style.textContent = `
          [contenteditable="true"]:focus {
            outline: none !important;
            border: none !important;
          }
        `;
        document.head.appendChild(style);
      }

      // 启用所有文本元素的contentEditable
      const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, td, th, li, blockquote');
      let editableCount = 0;

      textElements.forEach(element => {
        // 只对包含文本内容且没有子元素（或子元素也是文本节点）的元素启用编辑
        const hasOnlyTextContent = element.children.length === 0 ||
          (element.textContent && element.textContent.trim().length > 0);

        if (hasOnlyTextContent && element.textContent && element.textContent.trim()) {
          element.contentEditable = 'true';
          element.setAttribute('data-editable', 'true');
          editableCount++;
        }
      });

      console.log('启用手动编辑模式，可编辑元素数量：' + editableCount);
    ''';

    try {
      await _webviewKey.currentState?.executeScript(script);
      setState(() {
        _isManualEditEnabled = true;
      });
      // 不显示提示
    } catch (e) {
      print('启用手动编辑模式失败: $e');
      CustomToast.show('启用手动编辑模式失败');
    }
  }

  /// 禁用手动编辑模式
  Future<void> _disableManualEdit() async {
    const script = '''
      // 禁用所有contentEditable
      const editableElements = document.querySelectorAll('[data-editable="true"]');
      editableElements.forEach(element => {
        element.contentEditable = 'false';
        element.removeAttribute('data-editable');
      });

      // 移除CSS样式
      const style = document.getElementById('contenteditable-style');
      if (style) {
        style.remove();
      }

      console.log('已禁用手动编辑模式');
    ''';

    try {
      await _webviewKey.currentState?.executeScript(script);
      setState(() {
        _isManualEditEnabled = false;
      });
    } catch (e) {
      print('禁用手动编辑模式失败: $e');
    }
  }

  /// 处理保存操作
  Future<void> _onSave() async {
    try {
      // 1. 显示二次确认弹窗
      final confirmed = await PublishConfirmationDialog.show(context);
      if (!confirmed) {
        return;
      }

      // 2. 如果启用了手动编辑模式，先禁用它
      if (_isManualEditEnabled) {
        await _disableManualEdit();
      }

      // 3. 获取当前页面的HTML结构
      final htmlContent = await _onGetLatestDom();
      if (htmlContent == null || htmlContent.isEmpty) {
        CustomToast.show('获取页面内容失败');
        return;
      }

      // 3. 调用更新笔记接口
      final apiProvider = ApiProvider();
      final noteId = _noteModel.noteId;

      if (noteId.isEmpty) {
        CustomToast.show('笔记ID无效');
        return;
      }

      CustomToast.show('正在发布笔记...');

      final response = await apiProvider.noteApi.updateNote(
        id: noteId,
        title: _noteModel.title,
        html: htmlContent,
      );

      // 4. 检查响应结果
      if (response['code'] == 0) {
        CustomToast.show('笔记发布成功');

        // 5. 如果是从草稿进入的，删除对应的草稿
        if (_isDraftMode) {
          try {
            await apiProvider.draftApi.deleteDraftByNoteId(noteId);
            debugPrint('草稿删除成功: noteId=$noteId');
          } catch (e) {
            debugPrint('删除草稿失败: $e');
            // 草稿删除失败不影响发布成功的流程，只记录日志
          }
        }

        // 6. 跳转到笔记列表页面
        if (mounted) {
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/notes',
            (route) => false,
          );
        }
      } else {
        final message = response['message'] ?? '发布失败';
        CustomToast.show(message);
      }
    } catch (e) {
      debugPrint('发布笔记失败: $e');
      CustomToast.show('发布笔记失败: $e');
    }
  }







  /// 构建应用栏（返回null表示不显示）
  PreferredSizeWidget? _buildAppBar() {
    return null;
  }

  /// 构建页面主体
  Widget _buildBody() {
    if (_noteModel.isLoading) {
      return const NoteLoading(message: '正在加载笔记内容...');
    }

    if (_noteModel.isError) {
      return NoteError(
        message: _noteModel.errorMessage ?? '未知错误',
        onRetry: _onRetry,
        onBack: _onBack,
      );
    }

    return NoteWebview(
      key: _webviewKey,
      noteModel: _noteModel,
      onPageStarted: _onWebViewPageStarted,
      onPageFinished: _onWebViewPageFinished,
      onPageError: _onWebViewPageError,
    );
  }

  /// 构建底部工具栏（返回null表示不显示）
  Widget? _buildBottomToolbar() {
    return null;
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: AppColors.background,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return WillPopScope(
      onWillPop: () async {
        if (_isPanelVisible) {
          // 如果底部面板打开，先关闭面板
          setState(() {
            _isPanelVisible = false;
          });
          return false;
        } else if (_isPreviewMode) {
          // 如果在预览模式，退出预览模式
          setState(() {
            _isPreviewMode = false;
          });
          CustomToast.show('已退出预览模式');
          return false;
        }
        return true;
      },
      child: Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          _buildBody(),
          // 右侧操作按钮（预览模式时只显示预览按钮）
          NoteActionButtons(
            onAiEdit: _isPreviewMode ? null : _onOpenPanel,
            onUndo: _isPreviewMode ? null : _onUndo,
            onPreview: _onPreview,
            onSave: _isPreviewMode ? null : _onSave,
            isPreviewMode: _isPreviewMode,
            showUndoButton: _historyStack.isNotEmpty,
          ),
          // 底部面板组件
          NoteBottomPanel(
            isVisible: _isPanelVisible,
            onClose: _onClosePanel,
            htmlContent: _noteModel.htmlContent,
            onExecuteScript: _onExecuteScript,
            onGetLatestDom: _onGetLatestDom,
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomToolbar(),
    ),
    );
  }
}
